require File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "scripts/react_native_pods")

require 'json'
podfile_properties = JSON.parse(File.read(File.join(__dir__, 'Podfile.properties.json'))) rescue {}

ENV['RCT_NEW_ARCH_ENABLED'] = podfile_properties['newArchEnabled'] == 'true' ? '1' : '0'

platform :ios, '16.0'
install! 'cocoapods',
  :deterministic_uuids => false

prepare_react_native_project!

# Mapbox Maps configuration
$RNMapboxMapsDownloadToken = '*****************************************************************************************'
$RNMapboxMapsVersion = '~> 10.18.0'

# If you want to use the latest mapbox SDK
# $RNMapboxMapsVersion = '= 11.8.0'

require_relative '../node_modules/@rnmapbox/maps/scripts/utils.rb'
$RNMapboxMaps = get_rnmapbox_config()

# Initialize RNMapboxMaps
def $RNMapboxMaps.pre_install(installer)
  # Handle Xcode 16 compatibility
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
      config.build_settings['SWIFT_VERSION'] = '5.0'
    end
  end
end

def $RNMapboxMaps.post_install(installer)
  # Handle Xcode 16 and iOS 18 compatibility
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
      config.build_settings['SWIFT_VERSION'] = '5.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      
      # Fix for Xcode 16 and newer
      if config.build_settings['PRODUCT_NAME'] == 'MapboxMaps'
        config.build_settings['SWIFT_COMPILATION_MODE'] = 'wholemodule'
        config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-Onone'
      end
    end
  end
end

target 'Fishivo' do
  config = use_native_modules!

  use_frameworks! :linkage => podfile_properties['ios.useFrameworks'].to_sym if podfile_properties['ios.useFrameworks']
  use_frameworks! :linkage => ENV['USE_FRAMEWORKS'].to_sym if ENV['USE_FRAMEWORKS']

  # Mapbox Maps pre-install
  pre_install do |installer|
    $RNMapboxMaps.pre_install(installer)
  end

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => podfile_properties['hermes'] != 'false',
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :privacy_file_aggregation_enabled => podfile_properties['apple.privacyManifestAggregationEnabled'] != 'false',
  )

  post_install do |installer|
    # Mapbox Maps post-install
    $RNMapboxMaps.post_install(installer)
    
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
    )
  end
end
