// Test file to verify all hooks are properly exported and importable
// Using a simple approach to avoid React Native import issues
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Fishivo Hooks Package...\n');

// Read the TypeScript definitions to check exports
const dtsPath = path.join(__dirname, 'dist', 'index.d.ts');
const dtsContent = fs.readFileSync(dtsPath, 'utf8');

// Extract exports from the last line
const exportLine = dtsContent.split('\n').find(line => line.startsWith('export {'));
const actualExports = exportLine
  ? exportLine.match(/export \{ (.+) \}/)[1]
    .split(',')
    .map(exp => {
      const trimmed = exp.trim();
      // Handle "originalName as newName" format
      if (trimmed.includes(' as ')) {
        return trimmed.split(' as ')[1];
      }
      return trimmed;
    })
    .filter(exp => exp && !exp.includes('$'))
  : [];

console.log('📋 Found exports in TypeScript definitions:', actualExports.length);

// Test all expected exports
const expectedExports = [
  // Authentication hooks
  'useAuth',
  'useProfile',
  
  // Social hooks
  'useFollow',
  'useLikes',
  
  // Data hooks
  'useCatches',
  'useUsers',
  
  // Location and Weather hooks
  'useLocation',
  'useWeather',
  
  // Utility hooks
  'useImagePicker',
  'useForm',
  'useValidation',
  'useStorage',
  
  // Navigation hooks
  'useNavigation',
  'useRoute',
  
  // Core API hooks
  'useApi',
  'useSupabase',
  
  // Contexts
  'AuthProvider',
  'FollowProvider',
  'UnitsProvider',
  'LocationProvider',
  'useAuthContext',
  'useFollowContext',
  'useUnits',
];

let passedTests = 0;
let failedTests = 0;

console.log('📋 Checking exports...\n');

expectedExports.forEach(exportName => {
  if (actualExports.includes(exportName)) {
    console.log(`✅ ${exportName} - exported successfully`);
    passedTests++;
  } else {
    console.log(`❌ ${exportName} - missing export`);
    failedTests++;
  }
});

console.log('\n📊 Test Results:');
console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`📦 Total exports found: ${actualExports.length}`);

if (failedTests === 0) {
  console.log('\n🎉 All hooks exported successfully!');
  console.log('🚀 Fishivo hooks package is ready for production use!');
} else {
  console.log('\n⚠️  Some exports are missing. Please check the implementation.');
}

console.log('\n📋 All available exports:');
actualExports.sort().forEach(key => {
  console.log(`  - ${key}`);
});
